import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile, balanceCheckLogs } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { randomUUID } from 'crypto';
import { getTokenRequirements } from '@/app/utils/systemSettings';
import { checkWalletBalanceWithRequirements, hasAnyTokenRequirements } from '@/lib/tokenValidation';

export async function POST(request: NextRequest): Promise<Response> {
  const jobId = randomUUID();
  let totalChecked = 0;
  let totalStatusChanged = 0;
  let totalErrors = 0;
  const statusChangedProfiles: Array<{
    address: string;
    name: string;
    chain: string;
    oldStatus: string;
    newStatus: string;
    balanceCheckResult: any;
    tokenRequirements: any;
  }> = [];

  try {
    console.log(`[Bulk Approve Job ${jobId}] Starting bulk approve check for all pending profiles`);

    // Get all pending profiles
    const pendingProfiles = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.status, 'pending'));

    console.log(`[Bulk Approve Job ${jobId}] Found ${pendingProfiles.length} pending profiles to check`);

    if (pendingProfiles.length === 0) {
      return Response.json({
        success: true,
        jobId,
        message: 'No pending profiles found to check',
        summary: {
          totalChecked: 0,
          totalStatusChanged: 0,
          totalErrors: 0
        }
      });
    }

    // Process each profile
    for (const profile of pendingProfiles) {
      totalChecked++;
      let statusChanged = false;
      let errorMessage: string | undefined;
      let balanceCheckResult: any = null;
      let tokenRequirements: any = null;

      try {
        console.log(`[Bulk Approve Job ${jobId}] Checking profile ${profile.address} on chain ${profile.chain}`);

        // Get token requirements for this chain
        tokenRequirements = await getTokenRequirements(profile.chain);

        if (!tokenRequirements) {
          console.log(`[Bulk Approve Job ${jobId}] No token requirements found for chain ${profile.chain}, skipping profile ${profile.address}`);
          continue;
        }

        // Check if there are any token requirements configured
        const hasRequirements = hasAnyTokenRequirements(tokenRequirements);
        if (!hasRequirements) {
          console.log(`[Bulk Approve Job ${jobId}] No token requirements configured for chain ${profile.chain}, auto-approving profile ${profile.address}`);

          // No token requirements means auto-approve
          await db
            .update(web3Profile)
            .set({
              status: 'approved',
              expiryDate: null, // Manual approval sets expiry to null
              updatedAt: new Date()
            })
            .where(eq(web3Profile.address, profile.address));

          statusChanged = true;
          totalStatusChanged++;

          statusChangedProfiles.push({
            address: profile.address,
            name: profile.name || 'Unnamed Profile',
            chain: profile.chain,
            oldStatus: profile.status,
            newStatus: 'approved',
            balanceCheckResult: {
              canProceed: true,
              validationPath: 'no-requirements',
              message: 'No token requirements configured - auto-approved'
            },
            tokenRequirements: null
          });

          // Log the operation
          await db.insert(balanceCheckLogs).values({
            jobId,
            profileAddress: profile.address,
            chainId: profile.chain,
            oldStatus: profile.status,
            newStatus: 'approved',
            balanceCheckResult: {
              canProceed: true,
              validationPath: 'no-requirements',
              message: 'No token requirements configured - auto-approved'
            },
            tokenRequirements: null,
            statusChanged: 'Y',
            errorMessage: undefined
          });

          continue;
        }

        console.log(`[Bulk Approve Job ${jobId}] Token requirements for chain ${profile.chain}:`, {
          mainRequirement: tokenRequirements.mainRequirement ? {
            tokenName: tokenRequirements.mainRequirement.tokenName,
            tokenAddress: tokenRequirements.mainRequirement.tokenAddress,
            minimumHolding: tokenRequirements.mainRequirement.minimumHolding
          } : null,
          secondaryRequirements: tokenRequirements.secondaryRequirements?.length || 0
        });

        // Check wallet balance
        balanceCheckResult = await checkWalletBalanceWithRequirements(
          profile.address,
          tokenRequirements,
          profile.chain
        );

        console.log(`[Bulk Approve Job ${jobId}] Balance check result for ${profile.address}:`, {
          canProceed: balanceCheckResult.canProceed,
          validationPath: balanceCheckResult.validationPath
        });

        // If balance check passes, update status from pending to approved
        if (balanceCheckResult.canProceed) {
          console.log(`[Bulk Approve Job ${jobId}] Profile ${profile.address} meets token requirements, updating status to 'approved'`);

          // Update profile status
          await db
            .update(web3Profile)
            .set({
              status: 'approved',
              expiryDate: null, // Manual approval sets expiry to null
              updatedAt: new Date()
            })
            .where(eq(web3Profile.address, profile.address));

          statusChanged = true;
          totalStatusChanged++;

          // Add to status changed profiles list
          statusChangedProfiles.push({
            address: profile.address,
            name: profile.name || 'Unnamed Profile',
            chain: profile.chain,
            oldStatus: profile.status,
            newStatus: 'approved',
            balanceCheckResult,
            tokenRequirements
          });
        }

      } catch (error) {
        totalErrors++;
        errorMessage = error instanceof Error ? error.message : 'Unknown error during balance check';
        console.error(`[Bulk Approve Job ${jobId}] Error checking profile ${profile.address}:`, error);
      }

      // Log the operation
      await db.insert(balanceCheckLogs).values({
        jobId,
        profileAddress: profile.address,
        chainId: profile.chain,
        oldStatus: profile.status,
        newStatus: statusChanged ? 'approved' : profile.status,
        balanceCheckResult,
        tokenRequirements,
        statusChanged: statusChanged ? 'Y' : 'N',
        errorMessage
      });
    }

    const summary = {
      totalChecked,
      totalStatusChanged,
      totalErrors,
      statusChangedProfiles
    };

    console.log(`[Bulk Approve Job ${jobId}] Completed. Summary:`, summary);

    return Response.json({
      success: true,
      jobId,
      message: `Bulk approve completed. Checked ${totalChecked} profiles, ${totalStatusChanged} approved, ${totalErrors} errors.`,
      summary
    });

  } catch (error) {
    console.error(`[Bulk Approve Job ${jobId}] Fatal error:`, error);
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during bulk approve',
      jobId,
      summary: {
        totalChecked,
        totalStatusChanged,
        totalErrors
      }
    }, { status: 500 });
  }
}
