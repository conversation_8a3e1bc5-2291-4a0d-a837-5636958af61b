import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile, componentPositions } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

// GET all users with their details
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Get all profiles with their details
    const profiles = await db.select().from(web3Profile);

    return Response.json(profiles, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    return Response.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST to update a user's role or status
export async function POST(request: NextRequest): Promise<Response> {
  try {
    const data = await request.json();
    const { address, role, status, expiryDate, transactionHash } = data;

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (existingUser.length === 0) {
      return Response.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (role !== undefined) updateData.role = role;
    if (status !== undefined) updateData.status = status;
    if (expiryDate !== undefined) updateData.expiryDate = expiryDate ? new Date(expiryDate) : null;
    if (transactionHash !== undefined) updateData.transactionHash = transactionHash;

    // Update user
    await db.update(web3Profile)
      .set(updateData)
      .where(eq(web3Profile.address, address));

    return Response.json({ success: true });
  } catch (error) {
    console.error('Failed to update user:', error);
    return Response.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}
