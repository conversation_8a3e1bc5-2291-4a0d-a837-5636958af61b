import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { componentPositions, componentImages, web3Profile } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { base64ToDataUrl } from '@/lib/imageUtils';
import { getComponentDefaults } from '@/app/utils/systemSettings';

// Define the type for the bannerpfp details object
interface BannerPfpDetails {
  profileShape?: string;
  profileHorizontalPosition?: number;
  profileNameHorizontalPosition?: number;
  profileNameStyle?: {
    fontSize?: string;
    fontWeight?: string;
    fontColor?: string;
    effect?: string;
    [key: string]: any;
  };
  profileName?: string;
  profileBio?: string;
  backgroundColor?: string;
  fontColor?: string | null;
  [key: string]: any; // Allow other properties
}

type Context = {
  params: Promise<{
    address: string;
  }>;
};

// GET bannerpfp data
export async function GET(
  request: NextRequest,
  context: Context
): Promise<Response> {
  const { address } = await context.params;

  if (!address) {
    return Response.json(
      { error: 'Address is required' },
      { status: 400 }
    );
  }

  try {
    // Get bannerpfp component data from componentPositions table
    const bannerpfpData = await db.select().from(componentPositions).where(
      and(
        eq(componentPositions.address, address),
        eq(componentPositions.componentType, 'bannerpfp')
      )
    );

    // Get profile data to retrieve the name field
    const profileData = await db.select().from(web3Profile).where(
      eq(web3Profile.address, address)
    );

    // Get the name from the profile data
    const urlName = profileData.length > 0 ? profileData[0].name : null;

    // Get banner image from componentImages table
    const bannerImages = await db.select().from(componentImages).where(
      and(
        eq(componentImages.address, address),
        eq(componentImages.componentType, 'bannerpfp'),
        eq(componentImages.section, 'banner')
      )
    );

    // Get profile image from componentImages table
    const profileImages = await db.select().from(componentImages).where(
      and(
        eq(componentImages.address, address),
        eq(componentImages.componentType, 'bannerpfp'),
        eq(componentImages.section, 'profile')
      )
    );

    // If no bannerpfp component found, try to create it with default images
    if (bannerpfpData.length === 0) {


      try {
        // Get default images from public directory
        const { readFileSync } = await import('fs');
        const { join } = await import('path');
        const { base64ToDataUrl } = await import('@/lib/imageUtils');

        // Read default banner image
        const bannerPath = join(process.cwd(), 'public', 'banner.png');
        const bannerBuffer = readFileSync(bannerPath);
        const bannerBase64 = bannerBuffer.toString('base64');
        const bannerDataUrl = base64ToDataUrl(bannerBase64);

        // Read default profile image
        const profilePath = join(process.cwd(), 'public', 'pfp.jpg');
        const profileBuffer = readFileSync(profilePath);
        const profileBase64 = profileBuffer.toString('base64');
        const profileDataUrl = base64ToDataUrl(profileBase64);

        // Instead of returning default images, create a record in the database first


        // Get profile data from web3Profile first to get the chain
        const profileData = await db.select().from(web3Profile).where(
          eq(web3Profile.address, address)
        );

        if (profileData.length === 0) {
          return Response.json(
            { error: 'No existing profile found. Please ensure your profile exists first.' },
            { status: 400 }
          );
        }
        const chain = profileData[0].chain;

        // Get component defaults from system settings
        const componentDefaults = await getComponentDefaults(chain);
        const bannerpfpDefaults = componentDefaults.find(comp => comp.componentType === 'bannerpfp');

        // Insert new record with default values
        await db.insert(componentPositions).values({
          address,
          chain,
          componentType: 'bannerpfp',
          order: bannerpfpDefaults?.order || '5', // Default order after other components
          hidden: bannerpfpDefaults?.hidden || 'N',
          details: {
            profileShape: 'circular',
            profileHorizontalPosition: 50,
            profileNameHorizontalPosition: 50,
            profileNameStyle: {
              fontSize: '1.5rem',
              fontWeight: 'bold',
              fontColor: '#ffffff',
              effect: 'typewriter'
            },
            profileName: address.substring(0, 8),
            profileBio: '',
            backgroundColor: '#000000',
            fontColor: '#ffffff'
          },
          createdAt: new Date(),
          updatedAt: new Date()
        });

        // Now fetch the newly created record
        const newRecord = await db.select().from(componentPositions).where(
          and(
            eq(componentPositions.address, address),
            eq(componentPositions.componentType, 'bannerpfp')
          )
        );

        // Cast the details to the correct type
        const details = newRecord[0].details as BannerPfpDetails;

        // Return the data from the database
        return Response.json({
          bannerUrl: bannerDataUrl,
          bannerScale: 1,
          bannerPosition: { x: 0, y: 0 },
          bannerNaturalSize: null,
          profileUrl: profileDataUrl,
          profileScale: 1,
          profilePosition: { x: 0, y: 0 },
          profileNaturalSize: null,
          profileShape: details.profileShape,
          profileHorizontalPosition: details.profileHorizontalPosition,
          profileNameHorizontalPosition: details.profileNameHorizontalPosition,
          profileNameStyle: details.profileNameStyle,
          profileName: details.profileName,
          profileBio: details.profileBio,
          urlName: urlName || details.profileName
        });
      } catch (error) {
        console.error('Error creating default bannerpfp component:', error);

        // Try a simpler approach to create a database record
        try {
          // Get profile data from web3Profile
          const profileData = await db.select().from(web3Profile).where(
            eq(web3Profile.address, address)
          );

          const chain = profileData.length > 0 ? profileData[0].chain : '25'; // Default to Cronos chain

          // Insert new record with minimal default values
          await db.insert(componentPositions).values({
            address,
            chain,
            componentType: 'bannerpfp',
            order: '5',
            hidden: 'N',
            details: {
              profileShape: 'circular',
              profileHorizontalPosition: 50,
              profileNameHorizontalPosition: 50,
              profileName: address.substring(0, 8),
            },
            createdAt: new Date(),
            updatedAt: new Date()
          });

          // Fetch the newly created record
          const newRecord = await db.select().from(componentPositions).where(
            and(
              eq(componentPositions.address, address),
              eq(componentPositions.componentType, 'bannerpfp')
            )
          );

          // Cast the details to the correct type
          const details = newRecord[0].details as BannerPfpDetails;

          // Return minimal data from the database
          return Response.json({
            bannerUrl: null,
            bannerScale: 1,
            bannerPosition: { x: 0, y: 0 },
            bannerNaturalSize: null,
            profileUrl: null,
            profileScale: 1,
            profilePosition: { x: 0, y: 0 },
            profileNaturalSize: null,
            profileShape: details.profileShape,
            profileHorizontalPosition: details.profileHorizontalPosition,
            profileNameHorizontalPosition: details.profileNameHorizontalPosition,
            profileNameStyle: details.profileNameStyle,
            profileName: details.profileName,
            profileBio: details.profileBio,
            urlName: urlName
          });
        } catch (innerError) {
          console.error('Failed to create minimal bannerpfp component:', innerError);

          // If all else fails, return an error response
          return Response.json(
            { error: 'Failed to create bannerpfp component' },
            { status: 500 }
          );
        }
      }
    }

    const bannerpfp = bannerpfpData[0];
    const bannerImage = bannerImages.length > 0 ? bannerImages[0] : null;
    const profileImage = profileImages.length > 0 ? profileImages[0] : null;

    // If no images found, try to use default images
    if (!bannerImage || !profileImage) {


      try {
        // Get default images from public directory
        const { readFileSync } = await import('fs');
        const { join } = await import('path');
        const { saveImage } = await import('@/lib/imageStorage');

        // Read default banner image if needed
        let bannerDataUrl = null;
        let bannerBase64 = null;
        if (!bannerImage) {
          const bannerPath = join(process.cwd(), 'public', 'banner.png');
          const bannerBuffer = readFileSync(bannerPath);
          bannerBase64 = bannerBuffer.toString('base64');
          bannerDataUrl = base64ToDataUrl(bannerBase64);

          // Save the default banner image to the database for future use

          await saveImage(address, 'bannerpfp', 'banner', bannerBase64, {
            scale: 1,
            positionX: 0,
            positionY: 0
          });
        } else {
          bannerDataUrl = bannerImage.imageData ? base64ToDataUrl(bannerImage.imageData) : null;
        }

        // Read default profile image if needed
        let profileDataUrl = null;
        let profileBase64 = null;
        if (!profileImage) {
          const profilePath = join(process.cwd(), 'public', 'pfp.jpg');
          const profileBuffer = readFileSync(profilePath);
          profileBase64 = profileBuffer.toString('base64');
          profileDataUrl = base64ToDataUrl(profileBase64);

          // Save the default profile image to the database for future use

          await saveImage(address, 'bannerpfp', 'profile', profileBase64, {
            scale: 1,
            positionX: 0,
            positionY: 0
          });
        } else {
          profileDataUrl = profileImage.imageData ? base64ToDataUrl(profileImage.imageData) : null;
        }

        // Get component details from the bannerpfp component and cast to the correct type
        const componentDetails: BannerPfpDetails = bannerpfp.details || {};

        // Return with default images where needed but no fallbacks for component details
        return Response.json({
          bannerUrl: bannerDataUrl,
          bannerScale: bannerImage ? bannerImage.scale : 1,
          bannerPosition: bannerImage ? { x: bannerImage.positionX, y: bannerImage.positionY } : { x: 0, y: 0 },
          bannerNaturalSize: bannerImage?.naturalWidth && bannerImage?.naturalHeight ?
            { width: bannerImage.naturalWidth, height: bannerImage.naturalHeight } : null,
          profileUrl: profileDataUrl,
          profileScale: profileImage ? profileImage.scale : 1,
          profilePosition: profileImage ? { x: profileImage.positionX, y: profileImage.positionY } : { x: 0, y: 0 },
          profileNaturalSize: profileImage?.naturalWidth && profileImage?.naturalHeight ?
            { width: profileImage.naturalWidth, height: profileImage.naturalHeight } : null,
          profileShape: componentDetails.profileShape,
          profileHorizontalPosition: componentDetails.profileHorizontalPosition,
          profileName: componentDetails.profileName,
          profileBio: componentDetails.profileBio,
          urlName: urlName
        });
      } catch (error) {
        console.error('Error using default images for bannerpfp component:', error);
      }
    }

    // Convert image data to data URLs
    const bannerDataUrl = bannerImage?.imageData ? base64ToDataUrl(bannerImage.imageData) : null;
    const profileDataUrl = profileImage?.imageData ? base64ToDataUrl(profileImage.imageData) : null;

    // Get component details from the bannerpfp component and cast to the correct type
    const componentDetails: BannerPfpDetails = bannerpfp.details || {};



    // Prepare the response data without fallbacks
    const responseData = {
      bannerUrl: bannerDataUrl,
      bannerScale: bannerImage ? bannerImage.scale : 1,
      bannerPosition: bannerImage ? { x: bannerImage.positionX, y: bannerImage.positionY } : { x: 0, y: 0 },
      bannerNaturalSize: bannerImage?.naturalWidth && bannerImage?.naturalHeight ?
        { width: bannerImage.naturalWidth, height: bannerImage.naturalHeight } : null,
      profileUrl: profileDataUrl,
      profileScale: profileImage ? profileImage.scale : 1,
      profilePosition: profileImage ? { x: profileImage.positionX, y: profileImage.positionY } : { x: 0, y: 0 },
      profileNaturalSize: profileImage?.naturalWidth && profileImage?.naturalHeight ?
        { width: profileImage.naturalWidth, height: profileImage.naturalHeight } : null,
      profileShape: componentDetails.profileShape,
      profileHorizontalPosition: componentDetails.profileHorizontalPosition,
      profileNameHorizontalPosition: componentDetails.profileNameHorizontalPosition,
      profileNameStyle: componentDetails.profileNameStyle,
      profileName: componentDetails.profileName,
      profileBio: componentDetails.profileBio,
      backgroundColor: componentDetails.backgroundColor,
      fontColor: componentDetails.fontColor,
      urlName: urlName
    };



    // Return bannerpfp data
    return Response.json(responseData);
  } catch (error) {

    return Response.json(
      { error: 'Failed to get bannerpfp data' },
      { status: 500 }
    );
  }
}

// POST to update bannerpfp data
export async function POST(
  request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address } = await context.params;
    const metadata = await request.json();


    const bannerUrl = metadata.bannerUrl;
    const bannerScale = metadata.bannerScale;
    const bannerPosition = metadata.bannerPosition;
    const bannerNaturalSize = metadata.bannerNaturalSize;
    const profileUrl = metadata.profileUrl;
    const profileScale = metadata.profileScale;
    const profilePosition = metadata.profilePosition;
    const profileNaturalSize = metadata.profileNaturalSize;
    const profileShape = metadata.profileShape;
    const profileHorizontalPosition = metadata.profileHorizontalPosition;
    const profileName = metadata.profileName;
    const profileBio = metadata.profileBio;
    const urlName = metadata.urlName;
    const backgroundColor = metadata.backgroundColor;
    const fontColor = metadata.fontColor;
    const profileNameHorizontalPosition = metadata.profileNameHorizontalPosition;
    const profileNameStyle = metadata.profileNameStyle;

    // Check if we already have a record for this address
    const existingRecord = await db.select().from(componentPositions).where(
      and(
        eq(componentPositions.address, address),
        eq(componentPositions.componentType, 'bannerpfp')
      )
    );

    // Get profile data from web3Profile
    const profileData = await db.select().from(web3Profile).where(
      eq(web3Profile.address, address)
    );

    const chain = profileData.length > 0 ? profileData[0].chain : undefined;

    if (!chain) {
      return Response.json(
        { error: 'Chain not found for address' },
        { status: 400 }
      );
    }

    if (existingRecord.length > 0) {
      // Get existing details
      const existingDetails = existingRecord[0].details || {};

      // Get component defaults from system settings
      const componentDefaults = await getComponentDefaults(chain);
      const bannerpfpDefaults = componentDefaults.find(comp => comp.componentType === 'bannerpfp');

      // Update existing record
      await db.update(componentPositions)
        .set({
          details: {
            ...existingDetails,
            profileShape: profileShape,
            profileHorizontalPosition: profileHorizontalPosition,
            profileNameHorizontalPosition: profileNameHorizontalPosition,
            profileNameStyle: profileNameStyle,
            profileName: profileName,
            profileBio: profileBio,
            backgroundColor: backgroundColor,
            fontColor: fontColor
          },
          updatedAt: new Date()
        })
        .where(
          and(
            eq(componentPositions.address, address),
            eq(componentPositions.componentType, 'bannerpfp')
          )
        );
    } else {
      // Get component defaults from system settings
      const componentDefaults = await getComponentDefaults(chain);
      const bannerpfpDefaults = componentDefaults.find(comp => comp.componentType === 'bannerpfp');

      // Insert new record
      await db.insert(componentPositions).values({
        address,
        chain,
        componentType: 'bannerpfp',
        order: bannerpfpDefaults?.order || '5', // Default order after other components
        hidden: bannerpfpDefaults?.hidden || 'N',
        details: {
          profileShape: profileShape,
          profileHorizontalPosition: profileHorizontalPosition,
          profileNameHorizontalPosition: profileNameHorizontalPosition,
          profileNameStyle: profileNameStyle,
          profileName: profileName,
          profileBio: profileBio,
          backgroundColor: backgroundColor,
          fontColor: fontColor
        },
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }

    // Save banner image data and positioning to componentImages table if provided
    if (bannerUrl) {
      // Extract base64 data from data URL
      const base64Data = bannerUrl.split(',')[1];

      // Check if we already have a record for this banner image
      const existingBannerImage = await db.select().from(componentImages).where(
        and(
          eq(componentImages.address, address),
          eq(componentImages.componentType, 'bannerpfp'),
          eq(componentImages.section, 'banner')
        )
      );

      if (existingBannerImage.length > 0) {
        // Update existing banner image
        await db.update(componentImages)
          .set({
            imageData: base64Data,
            scale: bannerScale,
            positionX: bannerPosition?.x || 0,
            positionY: bannerPosition?.y || 0,
            naturalWidth: bannerNaturalSize?.width || null,
            naturalHeight: bannerNaturalSize?.height || null,
            updatedAt: new Date()
          })
          .where(
            and(
              eq(componentImages.address, address),
              eq(componentImages.componentType, 'bannerpfp'),
              eq(componentImages.section, 'banner')
            )
          );
      } else {
        // Insert new banner image
        await db.insert(componentImages).values({
          id: crypto.randomUUID(),
          address,
          componentType: 'bannerpfp',
          section: 'banner',
          imageData: base64Data,
          scale: bannerScale || 1,
          positionX: bannerPosition?.x || 0,
          positionY: bannerPosition?.y || 0,
          naturalWidth: bannerNaturalSize?.width || null,
          naturalHeight: bannerNaturalSize?.height || null,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
    }

    // Save profile image data and positioning to componentImages table if provided
    if (profileUrl) {
      // Extract base64 data from data URL
      const base64Data = profileUrl.split(',')[1];

      // Check if we already have a record for this profile image
      const existingProfileImage = await db.select().from(componentImages).where(
        and(
          eq(componentImages.address, address),
          eq(componentImages.componentType, 'bannerpfp'),
          eq(componentImages.section, 'profile')
        )
      );

      if (existingProfileImage.length > 0) {
        // Update existing profile image
        await db.update(componentImages)
          .set({
            imageData: base64Data,
            scale: profileScale,
            positionX: profilePosition?.x || 0,
            positionY: profilePosition?.y || 0,
            naturalWidth: profileNaturalSize?.width || null,
            naturalHeight: profileNaturalSize?.height || null,
            updatedAt: new Date()
          })
          .where(
            and(
              eq(componentImages.address, address),
              eq(componentImages.componentType, 'bannerpfp'),
              eq(componentImages.section, 'profile')
            )
          );
      } else {
        // Insert new profile image
        await db.insert(componentImages).values({
          id: crypto.randomUUID(),
          address,
          componentType: 'bannerpfp',
          section: 'profile',
          imageData: base64Data,
          scale: profileScale || 1,
          positionX: profilePosition?.x || 0,
          positionY: profilePosition?.y || 0,
          naturalWidth: profileNaturalSize?.width || null,
          naturalHeight: profileNaturalSize?.height || null,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
    }

    // Update the name field in the web3Profile table
    if (urlName) {
      await db.update(web3Profile)
        .set({
          name: urlName,
          updatedAt: new Date()
        })
        .where(eq(web3Profile.address, address));
    }

    return Response.json({ message: 'Bannerpfp data updated successfully' });
  } catch (error) {
    console.error('Failed to update bannerpfp data:', error);
    return Response.json(
      { error: 'Failed to update bannerpfp data' },
      { status: 500 }
    );
  }
}
