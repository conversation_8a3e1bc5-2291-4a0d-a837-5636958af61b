import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { componentPositions } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

/**
 * POST /api/components/update-order
 * Updates the order of components for a profile
 */
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const { address, components } = data;

    if (!address) {
      return NextResponse.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    if (!components || !Array.isArray(components) || components.length === 0) {
      return NextResponse.json(
        { error: 'Components array is required and must not be empty' },
        { status: 400 }
      );
    }

    console.log(`Updating component order for address: ${address}`);
    console.log('Components to update:', components);

    // Update each component's order
    for (const component of components) {
      const { componentType, order } = component;

      if (!componentType || !order) {
        console.warn(`Skipping component with missing data: ${JSON.stringify(component)}`);
        continue;
      }

      // Update component order only, not changing visibility
      await db.update(componentPositions)
        .set({
          order,
          updatedAt: new Date()
        })
        .where(
          and(
            eq(componentPositions.address, address),
            eq(componentPositions.componentType, componentType)
          )
        );
    }

    // Get updated components
    const updatedComponents = await db
      .select()
      .from(componentPositions)
      .where(eq(componentPositions.address, address))
      .orderBy(componentPositions.order);

    return NextResponse.json({
      success: true,
      message: 'Component order updated successfully',
      components: updatedComponents
    });
  } catch (error) {
    console.error('Failed to update component order:', error);
    return NextResponse.json(
      { error: 'Failed to update component order' },
      { status: 500 }
    );
  }
}
