import { NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { componentPositions } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const address = searchParams.get('address');
  const type = searchParams.get('type'); // 'bannerpfp'

  if (!address || !type) {
    return NextResponse.json(
      { error: 'Address and type are required' },
      { status: 400 }
    );
  }

  try {
    if (type === 'bannerpfp') {
      // Get the component type
      const componentType = 'bannerpfp';

      // Get the existing component
      const existingComponent = await db
        .select()
        .from(componentPositions)
        .where(
          and(
            eq(componentPositions.address, address),
            eq(componentPositions.componentType, componentType)
          )
        );

      // Delete the image from componentImages table
      const { deleteComponentImages } = await import('@/lib/imageStorage');
      await deleteComponentImages(address, componentType);

      // Update the component's updatedAt timestamp
      if (existingComponent.length > 0) {
        await db.update(componentPositions)
          .set({
            updatedAt: new Date()
          })
          .where(
            and(
              eq(componentPositions.address, address),
              eq(componentPositions.componentType, componentType)
            )
          );
      }
    } else {
      return NextResponse.json(
        { error: 'Invalid type. Expected "bannerpfp"' },
        { status: 400 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete image:', error);
    return NextResponse.json(
      { error: 'Failed to delete image' },
      { status: 500 }
    );
  }
}