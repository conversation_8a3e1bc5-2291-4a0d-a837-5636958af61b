import { NextRequest } from 'next/server';
import { randomUUID } from 'crypto';

// In-memory storage for likes (for testing purposes)
const likesStore: Record<string, string[]> = {};

// GET all likes for a profile
export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const address = searchParams.get('address');

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    // Get likes for the profile
    const likes = likesStore[address] || [];
    const totalLikes = likes.length;

    // Check if the requesting user has liked this profile
    const likerAddress = searchParams.get('likerAddress');
    let hasLiked = false;

    if (likerAddress) {
      hasLiked = likes.includes(likerAddress);
    }

    return Response.json({
      totalLikes,
      hasLiked
    });
  } catch (error) {
    console.error('Failed to fetch likes:', error);
    return Response.json(
      { error: 'Failed to fetch likes' },
      { status: 500 }
    );
  }
}

// POST to toggle like status
export async function POST(request: NextRequest): Promise<Response> {
  try {
    const data = await request.json();
    const { likerAddress, likedAddress } = data;

    if (!likerAddress || !likedAddress) {
      return Response.json(
        { error: 'Liker address and liked address are required' },
        { status: 400 }
      );
    }

    // Initialize the likes array if it doesn't exist
    if (!likesStore[likedAddress]) {
      likesStore[likedAddress] = [];
    }

    // Check if the like already exists
    const likeIndex = likesStore[likedAddress].indexOf(likerAddress);
    let action: 'liked' | 'unliked';

    if (likeIndex !== -1) {
      // Unlike: Remove the like
      likesStore[likedAddress].splice(likeIndex, 1);
      action = 'unliked';
    } else {
      // Like: Add the like
      likesStore[likedAddress].push(likerAddress);
      action = 'liked';
    }

    // Count total likes for the profile
    const totalLikes = likesStore[likedAddress].length;

    return Response.json({
      action,
      totalLikes,
      hasLiked: action === 'liked'
    });
  } catch (error) {
    console.error('Failed to toggle like:', error);
    return Response.json(
      { error: 'Failed to toggle like' },
      { status: 500 }
    );
  }
}
