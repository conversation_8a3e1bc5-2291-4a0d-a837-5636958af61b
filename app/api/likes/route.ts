import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { profileLikes, web3Profile } from '@/db/schema';
import { eq, and, count } from 'drizzle-orm';
import { randomUUID } from 'crypto';
import { handleApiError } from '@/app/api/errorHandler';

// GET all likes for a profile
export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const address = searchParams.get('address');

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    // Check if profile exists
    const profile = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (profile.length === 0) {
      return Response.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // Count total likes for the profile
    const result = await db
      .select({ count: count() })
      .from(profileLikes)
      .where(eq(profileLikes.likedAddress, address));

    const totalLikes = result[0]?.count || 0;

    // Check if the requesting user has liked this profile
    const likerAddress = searchParams.get('likerAddress');
    let hasLiked = false;

    if (likerAddress) {
      // Check if liker profile exists
      const likerProfile = await db
        .select()
        .from(web3Profile)
        .where(eq(web3Profile.address, likerAddress));

      if (likerProfile.length === 0) {
        return Response.json(
          { error: 'Liker profile not found' },
          { status: 404 }
        );
      }

      const userLike = await db
        .select()
        .from(profileLikes)
        .where(
          and(
            eq(profileLikes.likedAddress, address),
            eq(profileLikes.likerAddress, likerAddress)
          )
        );

      hasLiked = userLike.length > 0;
    }

    return Response.json({
      totalLikes,
      hasLiked
    });
  } catch (error) {
    console.error('Failed to fetch likes:', error);
    return handleApiError(error);
  }
}

// POST to toggle like status
export async function POST(request: NextRequest): Promise<Response> {
  try {
    const data = await request.json();
    const { likerAddress, likedAddress } = data;

    if (!likerAddress || !likedAddress) {
      return Response.json(
        { error: 'Liker address and liked address are required' },
        { status: 400 }
      );
    }

    // Check if both addresses exist in web3Profile
    const likerProfile = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, likerAddress));

    const likedProfile = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, likedAddress));

    // Check if profiles exist
    if (likerProfile.length === 0) {
      return Response.json(
        { error: 'Liker profile not found' },
        { status: 404 }
      );
    }

    if (likedProfile.length === 0) {
      return Response.json(
        { error: 'Liked profile not found' },
        { status: 404 }
      );
    }

    // Check if the like already exists
    const existingLike = await db
      .select()
      .from(profileLikes)
      .where(
        and(
          eq(profileLikes.likerAddress, likerAddress),
          eq(profileLikes.likedAddress, likedAddress)
        )
      );

    let action: 'liked' | 'unliked';

    if (existingLike.length > 0) {
      // Unlike: Delete the existing like
      await db
        .delete(profileLikes)
        .where(
          and(
            eq(profileLikes.likerAddress, likerAddress),
            eq(profileLikes.likedAddress, likedAddress)
          )
        );
      action = 'unliked';
    } else {
      // Like: Insert a new like
      await db.insert(profileLikes).values({
        id: randomUUID(),
        likerAddress,
        likedAddress,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      action = 'liked';
    }

    // Count total likes for the profile after the update
    const result = await db
      .select({ count: count() })
      .from(profileLikes)
      .where(eq(profileLikes.likedAddress, likedAddress));

    const totalLikes = result[0]?.count || 0;

    return Response.json({
      action,
      totalLikes,
      hasLiked: action === 'liked'
    });
  } catch (error) {
    console.error('Failed to toggle like:', error);
    return handleApiError(error);
  }
}
