import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile, componentPositions, profileLikes } from '@/db/schema';
import { eq, and, not, sql, count, inArray } from 'drizzle-orm';
import { getOrCreateProfile } from '@/app/utils/profileDefaults';
import { getProfileDefaults, getComponentDefaults } from '@/app/utils/systemSettings';

// GET all profiles with optional filtering and sorting
export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const chainFilter = searchParams.get('chain');

    // No longer filtering by approval status - show all profiles for the chain

    // Execute query with grouping
    const profiles = chainFilter
      ? await db
          .select({
            address: web3Profile.address,
            chain: web3Profile.chain,
            name: web3Profile.name,
            role: web3Profile.role,
            status: web3Profile.status,
            createdAt: web3Profile.createdAt,
            updatedAt: web3Profile.updatedAt,
            likeCount: sql<number>`CAST(COUNT(${profileLikes.id}) AS UNSIGNED)`
          })
          .from(web3Profile)
          .leftJoin(profileLikes, eq(web3Profile.address, profileLikes.likedAddress))
          .where(eq(web3Profile.chain, chainFilter))
          .groupBy(web3Profile.address)
      : await db
          .select({
            address: web3Profile.address,
            chain: web3Profile.chain,
            name: web3Profile.name,
            role: web3Profile.role,
            status: web3Profile.status,
            createdAt: web3Profile.createdAt,
            updatedAt: web3Profile.updatedAt,
            likeCount: sql<number>`CAST(COUNT(${profileLikes.id}) AS UNSIGNED)`
          })
          .from(web3Profile)
          .leftJoin(profileLikes, eq(web3Profile.address, profileLikes.likedAddress))
          .groupBy(web3Profile.address);

    console.log(`Fetched ${profiles.length} profiles${chainFilter ? ` for chain ${chainFilter}` : ''}`);

    // Get bannerpfp components for profile names and bios
    const profileAddresses = profiles.map(p => p.address);

    let bannerpfpComponents: any[] = [];
    if (profileAddresses.length > 0) {
      bannerpfpComponents = await db
        .select()
        .from(componentPositions)
        .where(and(
          eq(componentPositions.componentType, 'bannerpfp'),
          inArray(componentPositions.address, profileAddresses)
        ));
    }

    // Combine profile data with component data for names and bios
    const profilesWithData = profiles.map(profile => {
      const bannerpfp = bannerpfpComponents.find(c => c.address === profile.address);
      const details = bannerpfp?.details as any;

      return {
        address: profile.address,
        urlName: profile.name || details?.urlName || profile.address.substring(0, 8), // URL-friendly name for navigation
        displayName: details?.profileName || profile.name || 'Unnamed Profile', // Display name for showing to users
        name: details?.profileName || profile.name || 'Unnamed Profile', // Keep for backward compatibility
        bio: details?.profileBio || '',
        createdAt: profile.createdAt,
        likeCount: profile.likeCount || 0,
        chain: profile.chain
      };
    });

    return Response.json(profilesWithData);
  } catch (error) {
    console.error('Failed to fetch profiles:', error);
    return Response.json(
      { error: 'Failed to fetch profiles' },
      { status: 500 }
    );
  }
}

// POST to create/update a profile
export async function POST(request: NextRequest): Promise<Response> {
  try {
    const data = await request.json();
    const { address, name, components, socialLinks, chain } = data;
    // bio removed - now handled in profile picture component

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    // Name validation removed - now handled in profile picture component

    // Name uniqueness check removed - now handled in profile picture component

    // Check if profile exists
    const existingProfileForAddress = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    // Create or update profile
    if (existingProfileForAddress.length === 0) {
      // Create new profile with database defaults
      await getOrCreateProfile(address, chain);
    } else {
      // Prepare update data
      const updateData: any = {
        // name removed - now handled in profile picture component
        // bio removed - now handled in profile picture component
        updatedAt: new Date(),
      };

      // socialLinks are now stored in componentPositions table details field
      // We'll update them separately if provided
      if (socialLinks !== undefined) {
        // Find the socialLinks component
        const socialLinksComponent = await db
          .select()
          .from(componentPositions)
          .where(and(
            eq(componentPositions.address, address),
            eq(componentPositions.componentType, 'socialLinks')
          ));

        if (socialLinksComponent.length > 0) {
          // Get existing details
          const existingDetails: {
            backgroundColor?: string;
            fontColor?: string | null;
            socialLinks?: any;
          } = (socialLinksComponent[0].details || {}) as any;

          // Get component defaults from system settings
          const componentDefaults = await getComponentDefaults(chain || '25');
          const socialLinksDefaults = componentDefaults.find(comp => comp.componentType === 'socialLinks');

          // Get default values from system settings
          const defaultDetails = socialLinksDefaults?.details;

          // Update existing socialLinks component using values from system settings
          await db.update(componentPositions)
            .set({
              details: {
                ...existingDetails,
                socialLinks,
                backgroundColor: existingDetails.backgroundColor,
                fontColor: existingDetails.fontColor
              },
              updatedAt: new Date()
            })
            .where(and(
              eq(componentPositions.address, address),
              eq(componentPositions.componentType, 'socialLinks')
            ));
        } else {
          // Get component defaults from system settings
          const componentDefaults = await getComponentDefaults(chain || '25');
          const socialLinksDefaults = componentDefaults.find(comp => comp.componentType === 'socialLinks');

          // Get default values from system settings
          const defaultDetails = socialLinksDefaults?.details;

          // Create new socialLinks component using values from system settings
          await db.insert(componentPositions).values({
            address: address,
            chain: chain || '25', // Ensure chain is never undefined
            componentType: 'socialLinks',
            order: socialLinksDefaults?.order || '5', // Default order if not provided
            hidden: socialLinksDefaults?.hidden || 'N', // Default to not hidden
            details: {
              socialLinks,
              backgroundColor: defaultDetails?.backgroundColor || 'transparent',
              fontColor: defaultDetails?.fontColor || null
            },
            createdAt: new Date(),
            updatedAt: new Date()
          });
        }
      }

      // Update existing profile
      await db
        .update(web3Profile)
        .set(updateData)
        .where(eq(web3Profile.address, address));
    }

    // Handle component positions
    if (components && Array.isArray(components)) {
      console.log('Saving components:', components);

      try {
        // Ensure default component positions exist
        await getOrCreateProfile(address, chain);

        // Update existing component positions
        for (const component of components) {
          if (!component.componentType) {
            console.warn('Component missing componentType:', component);
            continue;
          }

          // Check if component exists
          const existingComponent = await db
            .select()
            .from(componentPositions)
            .where(and(
              eq(componentPositions.address, address),
              eq(componentPositions.componentType, component.componentType)
            ));

          if (existingComponent.length > 0) {
            // Update existing component
            await db
              .update(componentPositions)
              .set({
                order: component.order,
                hidden: component.hidden,
                details: {
                  backgroundColor: component.backgroundColor,
                  fontColor: component.fontColor
                },
                updatedAt: new Date()
              })
              .where(and(
                eq(componentPositions.address, address),
                eq(componentPositions.componentType, component.componentType)
              ));
          } else {
            // Insert new component
            await db.insert(componentPositions).values({
              address,
              chain, // Add the chain field
              componentType: component.componentType,
              order: component.order,
              hidden: component.hidden,
              details: {
                backgroundColor: component.backgroundColor,
                fontColor: component.fontColor
              }
            });
          }
        }
      } catch (componentError) {
        console.error('Error saving components:', componentError);
        throw componentError;
      }
    }

    return Response.json({
      success: true,
      message: 'Profile saved successfully',
    });
  } catch (error) {
    console.error('Failed to save profile:', error);
    return Response.json(
      {
        error: 'Failed to save profile',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
