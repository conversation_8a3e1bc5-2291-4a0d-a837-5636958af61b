import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const address = searchParams.get('address');

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    // Get user profile by address
    const profile = await db
      .select({
        status: web3Profile.status,
        expiryDate: web3Profile.expiryDate,
        transactionHash: web3Profile.transactionHash
      })
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (profile.length === 0) {
      return Response.json(
        { status: 'new', isApproved: false },
        { status: 200 }
      );
    }

    const userProfile = profile[0];

    // Check if profile has expired
    const isExpired = userProfile.expiryDate && new Date(userProfile.expiryDate) < new Date();

    // If profile has expired, return 'expired' status
    if (isExpired) {
      return Response.json(
        {
          status: 'expired',
          isApproved: false,
          expiryDate: userProfile.expiryDate,
          transactionHash: userProfile.transactionHash
        },
        { status: 200 }
      );
    }

    // Return user status
    return Response.json(
      {
        status: userProfile.status,
        isApproved: userProfile.status === 'approved',
        expiryDate: userProfile.expiryDate,
        transactionHash: userProfile.transactionHash
      },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Failed to fetch user status:', error);

    // Check for database connection error
    if (error.code === 'ECONNREFUSED') {
      return Response.json(
        { error: 'Database is currently unavailable. Please try again later.' },
        { status: 503 }
      );
    }

    return Response.json(
      { error: 'Failed to fetch user status' },
      { status: 500 }
    );
  }
}
