import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { sniperConfigs } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

// GET - Fetch sniper configuration for a user and chain
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userAddress = searchParams.get('userAddress');
    const chainId = searchParams.get('chainId');

    if (!userAddress || !chainId) {
      return Response.json(
        { error: 'User address and chain ID are required' },
        { status: 400 }
      );
    }

    const config = await db
      .select()
      .from(sniperConfigs)
      .where(and(
        eq(sniperConfigs.userAddress, userAddress),
        eq(sniperConfigs.chainId, chainId)
      ))
      .limit(1);

    return Response.json({
      success: true,
      config: config[0] || null
    });

  } catch (error) {
    console.error('Error fetching sniper config:', error);
    return Response.json(
      { error: 'Failed to fetch sniper configuration' },
      { status: 500 }
    );
  }
}

// POST - Create or update sniper configuration
export async function POST(request: NextRequest) {
  try {
    const { userAddress, chainId, privateKey, buyAmount, slippage, autoBuyEnabled } = await request.json();

    // Validate required fields
    if (!userAddress || !chainId) {
      return Response.json(
        { error: 'User address and chain ID are required' },
        { status: 400 }
      );
    }

    // Validate address format
    if (!userAddress.match(/^0x[a-fA-F0-9]{40}$/)) {
      return Response.json(
        { error: 'Invalid user address format' },
        { status: 400 }
      );
    }

    // Validate private key format if provided
    if (privateKey && !privateKey.match(/^0x[a-fA-F0-9]{64}$/)) {
      return Response.json(
        { error: 'Invalid private key format' },
        { status: 400 }
      );
    }

    // Check if config already exists
    const existingConfig = await db
      .select()
      .from(sniperConfigs)
      .where(and(
        eq(sniperConfigs.userAddress, userAddress),
        eq(sniperConfigs.chainId, chainId)
      ))
      .limit(1);

    if (existingConfig.length > 0) {
      // Update existing config
      await db
        .update(sniperConfigs)
        .set({
          privateKey: privateKey || null,
          buyAmount: buyAmount || '10',
          slippage: slippage || '5',
          autoBuyEnabled: autoBuyEnabled ? 'Y' : 'N',
          updatedAt: new Date()
        })
        .where(and(
          eq(sniperConfigs.userAddress, userAddress),
          eq(sniperConfigs.chainId, chainId)
        ));

      return Response.json({
        success: true,
        message: 'Sniper configuration updated successfully'
      });
    } else {
      // Create new config
      const result = await db
        .insert(sniperConfigs)
        .values({
          userAddress,
          chainId,
          privateKey: privateKey || null,
          buyAmount: buyAmount || '10',
          slippage: slippage || '5',
          autoBuyEnabled: autoBuyEnabled ? 'Y' : 'N'
        });

      return Response.json({
        success: true,
        message: 'Sniper configuration created successfully'
      });
    }

  } catch (error) {
    console.error('Error saving sniper config:', error);
    return Response.json(
      { error: 'Failed to save sniper configuration' },
      { status: 500 }
    );
  }
}

// DELETE - Delete sniper configuration
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userAddress = searchParams.get('userAddress');
    const chainId = searchParams.get('chainId');

    if (!userAddress || !chainId) {
      return Response.json(
        { error: 'User address and chain ID are required' },
        { status: 400 }
      );
    }

    await db
      .delete(sniperConfigs)
      .where(and(
        eq(sniperConfigs.userAddress, userAddress),
        eq(sniperConfigs.chainId, chainId)
      ));

    return Response.json({
      success: true,
      message: 'Sniper configuration deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting sniper config:', error);
    return Response.json(
      { error: 'Failed to delete sniper configuration' },
      { status: 500 }
    );
  }
}
