import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { sniperCreators } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

// GET - Fetch all creators for a specific chain
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const chainId = searchParams.get('chainId');

    if (!chainId) {
      return Response.json(
        { error: 'Chain ID is required' },
        { status: 400 }
      );
    }

    const creators = await db
      .select()
      .from(sniperCreators)
      .where(eq(sniperCreators.chainId, chainId))
      .orderBy(sniperCreators.name);

    return Response.json({
      success: true,
      creators
    });

  } catch (error) {
    console.error('Error fetching creators:', error);
    return Response.json(
      { error: 'Failed to fetch creators' },
      { status: 500 }
    );
  }
}

// POST - Add a new creator
export async function POST(request: NextRequest) {
  try {
    const { address, name, chainId } = await request.json();

    // Validate required fields
    if (!address || !name || !chainId) {
      return Response.json(
        { error: 'Address, name, and chain ID are required' },
        { status: 400 }
      );
    }

    // Validate address format
    if (!address.match(/^0x[a-fA-F0-9]{40}$/)) {
      return Response.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    // Check if creator already exists
    const existingCreator = await db
      .select()
      .from(sniperCreators)
      .where(and(
        eq(sniperCreators.address, address),
        eq(sniperCreators.chainId, chainId)
      ))
      .limit(1);

    if (existingCreator.length > 0) {
      return Response.json(
        { error: 'Creator already exists for this chain' },
        { status: 409 }
      );
    }

    // Insert new creator
    const result = await db
      .insert(sniperCreators)
      .values({
        address,
        name,
        chainId,
        isActive: 'Y'
      });

    return Response.json({
      success: true,
      message: 'Creator added successfully',
      id: result[0].insertId
    });

  } catch (error) {
    console.error('Error adding creator:', error);
    return Response.json(
      { error: 'Failed to add creator' },
      { status: 500 }
    );
  }
}

// PUT - Update a creator
export async function PUT(request: NextRequest) {
  try {
    const { id, name, isActive } = await request.json();

    if (!id) {
      return Response.json(
        { error: 'Creator ID is required' },
        { status: 400 }
      );
    }

    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (isActive !== undefined) updateData.isActive = isActive;
    updateData.updatedAt = new Date();

    await db
      .update(sniperCreators)
      .set(updateData)
      .where(eq(sniperCreators.id, id));

    return Response.json({
      success: true,
      message: 'Creator updated successfully'
    });

  } catch (error) {
    console.error('Error updating creator:', error);
    return Response.json(
      { error: 'Failed to update creator' },
      { status: 500 }
    );
  }
}

// DELETE - Remove a creator
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return Response.json(
        { error: 'Creator ID is required' },
        { status: 400 }
      );
    }

    await db
      .delete(sniperCreators)
      .where(eq(sniperCreators.id, parseInt(id)));

    return Response.json({
      success: true,
      message: 'Creator removed successfully'
    });

  } catch (error) {
    console.error('Error removing creator:', error);
    return Response.json(
      { error: 'Failed to remove creator' },
      { status: 500 }
    );
  }
}
