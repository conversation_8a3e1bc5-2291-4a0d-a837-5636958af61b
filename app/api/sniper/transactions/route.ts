import { NextRequest } from 'next/server';
import { createPublicClient, http, formatUnits } from 'viem';
import { cronos } from 'viem/chains';

// Create a public client for Cronos chain
const publicClient = createPublicClient({
  chain: cronos,
  transport: http()
});

// Known factory contracts that create tokens
const TOKEN_FACTORY_CONTRACTS = [
  '0xe1412941248833b538df80810d68583b7e27f74c', // The factory from your example
  // Add more factory contracts here as you discover them
];

// Function to detect if transaction creates a new token
function analyzeTokenCreation(tx: any, receipt: any) {
  const result = {
    isTokenCreation: false,
    createdTokenAddress: null as string | null,
    tokenName: null as string | null,
    tokenSymbol: null as string | null,
    logs: [] as any[]
  };

  // Check if transaction is to a known factory contract
  const isFactoryTransaction = TOKEN_FACTORY_CONTRACTS.includes(tx.to?.toLowerCase() || '');

  if (!isFactoryTransaction || !receipt?.logs) {
    return result;
  }

  // Look for contract creation in logs and Transfer events from null address
  for (const log of receipt.logs) {
    // Transfer event signature: Transfer(address,address,uint256)
    const transferEventSignature = '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef';

    if (log.topics && log.topics[0] === transferEventSignature) {
      // Check if it's a mint (from null address)
      const fromAddress = log.topics[1];
      const nullAddress = '0x0000000000000000000000000000000000000000000000000000000000000000';

      if (fromAddress === nullAddress) {
        result.isTokenCreation = true;
        result.createdTokenAddress = log.address;
        result.logs.push(log);
        break;
      }
    }
  }

  return result;
}

interface Transaction {
  hash: string;
  from: string;
  to: string;
  value: string;
  timestamp: number;
  blockNumber: number;
  isTokenCreation?: boolean;
  createdTokenAddress?: string;
  tokenName?: string;
  tokenSymbol?: string;
  logs?: any[];
}

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const address = searchParams.get('address');
    const chainId = searchParams.get('chainId') || '25';
    const limit = parseInt(searchParams.get('limit') || '5');

    if (!address) {
      return Response.json(
        { error: 'Address parameter is required' },
        { status: 400 }
      );
    }

    // Validate address format
    if (!address.match(/^0x[a-fA-F0-9]{40}$/)) {
      return Response.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    console.log(`Fetching transactions for address: ${address} on chain: ${chainId}`);

    // Get the latest block number
    const latestBlock = await publicClient.getBlockNumber();
    console.log(`Latest block: ${latestBlock}`);

    // We'll check the last 100 blocks for transactions (more reasonable for RPC limits)
    const blocksToCheck = 100n;
    const startBlock = latestBlock - blocksToCheck;

    const transactions: Transaction[] = [];
    let foundTransactions = 0;
    let blocksChecked = 0;

    // Check blocks in reverse order (newest first), but limit to avoid RPC timeouts
    for (let blockNum = latestBlock; blockNum > startBlock && foundTransactions < limit && blocksChecked < 50; blockNum--) {
      try {
        blocksChecked++;

        const block = await publicClient.getBlock({
          blockNumber: blockNum,
          includeTransactions: true
        });

        if (block.transactions && Array.isArray(block.transactions)) {
          for (const tx of block.transactions) {
            // Check if transaction involves the target address
            if (typeof tx === 'object' && tx !== null && 'from' in tx && 'to' in tx) {
              const txFrom = tx.from?.toLowerCase();
              const txTo = tx.to?.toLowerCase();
              const targetAddr = address.toLowerCase();

              if (txFrom === targetAddr || txTo === targetAddr) {
                // Get transaction receipt to analyze logs for token creation
                let tokenCreationInfo = {
                  isTokenCreation: false,
                  createdTokenAddress: null as string | null,
                  tokenName: null as string | null,
                  tokenSymbol: null as string | null,
                  logs: [] as any[]
                };

                try {
                  const receipt = await publicClient.getTransactionReceipt({
                    hash: tx.hash as `0x${string}`
                  });
                  tokenCreationInfo = analyzeTokenCreation(tx, receipt);
                } catch (receiptError) {
                  console.error(`Error getting receipt for tx ${tx.hash}:`, receiptError);
                }

                transactions.push({
                  hash: tx.hash || '',
                  from: tx.from || '',
                  to: tx.to || '',
                  value: tx.value?.toString() || '0',
                  timestamp: Number(block.timestamp),
                  blockNumber: Number(blockNum),
                  isTokenCreation: tokenCreationInfo.isTokenCreation,
                  createdTokenAddress: tokenCreationInfo.createdTokenAddress || undefined,
                  tokenName: tokenCreationInfo.tokenName || undefined,
                  tokenSymbol: tokenCreationInfo.tokenSymbol || undefined,
                  logs: tokenCreationInfo.logs
                });

                foundTransactions++;
                if (foundTransactions >= limit) break;
              }
            }
          }
        }

        // Add a small delay to avoid overwhelming the RPC
        if (blocksChecked % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }

      } catch (blockError) {
        console.error(`Error fetching block ${blockNum}:`, blockError);
        // Continue with next block
        continue;
      }
    }

    // Sort transactions by block number (newest first)
    transactions.sort((a, b) => b.blockNumber - a.blockNumber);

    console.log(`Found ${transactions.length} transactions for address ${address}`);

    return Response.json({
      success: true,
      address,
      chainId,
      transactions: transactions.slice(0, limit),
      blocksChecked: blocksChecked,
      latestBlock: Number(latestBlock),
      totalFound: transactions.length
    });

  } catch (error) {
    console.error('Error fetching transactions:', error);
    
    // Return a more specific error message
    let errorMessage = 'Failed to fetch transactions';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return Response.json(
      { 
        error: errorMessage,
        success: false 
      },
      { status: 500 }
    );
  }
}
