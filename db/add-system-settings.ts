import { config } from "dotenv";
import { db } from './drizzle';
import { sql } from 'drizzle-orm';

config({ path: ".env" });

async function addSystemSettingsTable() {
  try {
    console.log('Adding system settings...');

    // Insert default settings with simplified multi-chain support
    await db.execute(sql`
      INSERT INTO system_settings (id, value)
      VALUES (
        'token_requirements',
        JSON_OBJECT(
          'selectedChain', '',
          'chainConfigs', JSON_ARRAY(
            JSON_OBJECT('id', '25', 'name', 'Cronos'),
            JSON_OBJECT('id', '1', 'name', 'Ethereum'),
            JSON_OBJECT('id', '137', 'name', 'Polygon'),
            JSON_OBJECT('id', '56', 'name', 'BNB Chain'),
            JSON_OBJECT('id', '42161', 'name', 'Arbitrum'),
            J<PERSON><PERSON>_OBJECT('id', '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', 'name', 'Solana')
          ),
          'tokenAddresses', JSON_OBJECT(
            '25', '******************************************',
            '1', '',
            '137', '',
            '56', '',
            '42161', '',
            '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', ''
          ),
          'tokenNames', JSON_OBJECT(
            '25', 'Web3Tools',
            '1', 'Web3Tools',
            '137', 'Web3Tools',
            '56', 'Web3Tools',
            '42161', 'Web3Tools',
            '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', 'Web3Tools'
          ),
          'minimumHoldings', JSON_OBJECT(
            '25', '0',
            '1', '0',
            '137', '0',
            '56', '0',
            '42161', '0',
            '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', '0'
          ),
          'burnAmounts', JSON_OBJECT(
            '25', '0',
            '1', '0',
            '137', '0',
            '56', '0',
            '42161', '0',
            '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', '0'
          )
        )
      )
      ON DUPLICATE KEY UPDATE
        value = JSON_OBJECT(
          'selectedChain', '',
          'chainConfigs', JSON_ARRAY(
            JSON_OBJECT('id', '25', 'name', 'Cronos'),
            JSON_OBJECT('id', '1', 'name', 'Ethereum'),
            JSON_OBJECT('id', '137', 'name', 'Polygon'),
            JSON_OBJECT('id', '56', 'name', 'BNB Chain'),
            JSON_OBJECT('id', '42161', 'name', 'Arbitrum'),
            JSON_OBJECT('id', '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', 'name', 'Solana')
          ),
          'tokenAddresses', JSON_OBJECT(
            '25', '******************************************',
            '1', '',
            '137', '',
            '56', '',
            '42161', '',
            '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', ''
          ),
          'tokenNames', JSON_OBJECT(
            '25', 'Web3Tools',
            '1', 'Web3Tools',
            '137', 'Web3Tools',
            '56', 'Web3Tools',
            '42161', 'Web3Tools',
            '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', 'Web3Tools'
          ),
          'minimumHoldings', JSON_OBJECT(
            '25', '0',
            '1', '0',
            '137', '0',
            '56', '0',
            '42161', '0',
            '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', '0'
          ),
          'burnAmounts', JSON_OBJECT(
            '25', '0',
            '1', '0',
            '137', '0',
            '56', '0',
            '42161', '0',
            '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', '0'
          )
        ),
        updated_at = CURRENT_TIMESTAMP
    `);

    // Insert component defaults with image settings
    await db.execute(sql`
      INSERT INTO system_settings (id, value)
      VALUES (
        'component_defaults',
        JSON_OBJECT(
          'defaults', JSON_ARRAY(
            JSON_OBJECT(
              'componentType', 'bannerpfp',
              'order', '1',
              'hidden', 'N',
              'details', JSON_OBJECT(
                'backgroundColor', 'transparent',
                'fontColor', NULL,
                'profileName', NULL,
                'profileBio', NULL,
                'profileShape', 'circular',
                'profileHorizontalPosition', 20,
                'profileNameHorizontalPosition', 50,
                'profileNameStyle', JSON_OBJECT(
                  'fontSize', '1.5rem',
                  'fontWeight', 'bold',
                  'fontColor', '#ffffff',
                  'effect', 'typewriter'
                ),
                'defaultBannerImagePath', 'banner.png',
                'defaultProfileImagePath', 'pfp.jpg'
              )
            ),
            JSON_OBJECT(
              'componentType', 'hero',
              'order', '2',
              'hidden', 'N',
              'details', JSON_OBJECT(
                'backgroundColor', 'transparent',
                'fontColor', NULL,
                'heroContent', JSON_ARRAY(
                  JSON_OBJECT(
                    'title', 'My First Section',
                    'description', 'This is my first section. Click edit to change this text.',
                    'contentType', 'color',
                    'colorGradient', 'linear-gradient(to bottom right, var(--cyan-500), var(--emerald-500))'
                  )
                )
              )
            ),
            JSON_OBJECT(
              'componentType', 'socialLinks',
              'order', '3',
              'hidden', 'N',
              'details', JSON_OBJECT(
                'backgroundColor', 'transparent',
                'fontColor', NULL,
                'socialLinks', JSON_OBJECT(
                  'twitter', 'https://x.com/Web3Tools_fun',
                  'discord', '',
                  'telegram', '',
                  'website', '',
                  'facebook', '',
                  'youtube', '',
                  'email', '',
                  'linkedin', '',
                  'cro', ''
                )
              )
            )
           )
        )
      )
      ON DUPLICATE KEY UPDATE
        value = JSON_OBJECT(
          'defaults', JSON_ARRAY(
            JSON_OBJECT(
              'componentType', 'bannerpfp',
              'order', '1',
              'hidden', 'N',
              'details', JSON_OBJECT(
                'backgroundColor', 'transparent',
                'fontColor', NULL,
                'profileName', NULL,
                'profileBio', NULL,
                'profileShape', 'circular',
                'profileHorizontalPosition', 50,
                'profileNameHorizontalPosition', 50,
                'profileNameStyle', JSON_OBJECT(
                  'fontSize', '1.5rem',
                  'fontWeight', 'bold',
                  'fontColor', '#ffffff',
                  'effect', 'typewriter'
                ),
                'defaultBannerImagePath', 'banner.png',
                'defaultProfileImagePath', 'pfp.jpg'
              )
            ),
            JSON_OBJECT(
              'componentType', 'hero',
              'order', '2',
              'hidden', 'N',
              'details', JSON_OBJECT(
                'backgroundColor', 'transparent',
                'fontColor', NULL,
                'heroContent', JSON_ARRAY(
                  JSON_OBJECT(
                    'title', 'My First Section',
                    'description', 'This is my first section. Click edit to change this text.',
                    'contentType', 'color',
                    'colorGradient', 'linear-gradient(to bottom right, var(--cyan-500), var(--emerald-500))'
                  )
                )
              )
            ),
            JSON_OBJECT(
              'componentType', 'socialLinks',
              'order', '3',
              'hidden', 'N',
              'details', JSON_OBJECT(
                'backgroundColor', 'transparent',
                'fontColor', NULL,
                'socialLinks', JSON_OBJECT(
                  'twitter', '',
                  'discord', '',
                  'telegram', '',
                  'website', '',
                  'facebook', '',
                  'youtube', '',
                  'email', '',
                  'linkedin', '',
                  'cro', ''
                )
              )
            )
          )
        ),
        updated_at = CURRENT_TIMESTAMP
    `);

    // Insert default profile settings
    await db.execute(sql`
      INSERT INTO system_settings (id, value)
      VALUES (
        'profile_defaults',
        JSON_OBJECT(
          'default_role', 'user',
          'default_status', 'new',
          'default_expiry_days', '1',
          'default_profile_name_format', 'Web3 User {address}',
          'default_profile_bio', 'Welcome to my Web3 Social profile!'
        )
      )
      ON DUPLICATE KEY UPDATE
        value = JSON_OBJECT(
          'default_role', 'user',
          'default_status', 'new',
          'default_expiry_days', '1',
          'default_profile_name_format', 'Web3 User {address}',
          'default_profile_bio', 'Welcome to my Web3 Social profile!'
        ),
        updated_at = CURRENT_TIMESTAMP
    `);

    console.log('Successfully inserted default system settings');
    return true;
  } catch (error) {
    console.error('Error adding system settings:', error);
    return false;
  }
}

// Export for use in reset-database.ts
export default addSystemSettingsTable;

// Run directly if this script is executed directly
if (require.main === module) {
  addSystemSettingsTable().then(() => process.exit(0)).catch(() => process.exit(1));
}
