import { config } from "dotenv";
import { db } from './drizzle';
import { sql } from 'drizzle-orm';

config({ path: ".env" });

async function checkTables() {
  try {
    console.log('Checking if tables exist...');

    // Check if systemSettings table exists
    const systemSettingsExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'system_settings'
    `);
    console.log('systemSettings table exists:', (systemSettingsExists as any)[0][0].count > 0);

    // Check if web3Profile table exists
    const web3ProfileExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'web3Profile'
    `);
    console.log('web3Profile table exists:', (web3ProfileExists as any)[0][0].count > 0);

    // Check if waitingList table exists
    const waitingListExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'waitingList'
    `);
    console.log('waitingList table exists:', (waitingListExists as any)[0][0].count > 0);

    // Check if componentPositions table exists
    const componentPositionsExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'componentPositions'
    `);
    console.log('componentPositions table exists:', (componentPositionsExists as any)[0][0].count > 0);

    // Check if componentImages table exists
    const componentImagesExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'componentImages'
    `);
    console.log('componentImages table exists:', (componentImagesExists as any)[0][0].count > 0);

    // Check if profileLikes table exists
    const profileLikesExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'profileLikes'
    `);
    console.log('profileLikes table exists:', (profileLikesExists as any)[0][0].count > 0);

    // Check if profileReferrals table exists
    const profileReferralsExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'profileReferrals'
    `);
    console.log('profileReferrals table exists:', (profileReferralsExists as any)[0][0].count > 0);

    // Check if token_holder_snapshots table exists
    const tokenHolderSnapshotsExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'token_holder_snapshots'
    `);
    console.log('token_holder_snapshots table exists:', (tokenHolderSnapshotsExists as any)[0][0].count > 0);

    // Check if token_holders table exists
    const tokenHoldersExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'token_holders'
    `);
    console.log('token_holders table exists:', (tokenHoldersExists as any)[0][0].count > 0);

    // Check if token_holder_jobs table exists
    const tokenHolderJobsExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'token_holder_jobs'
    `);
    console.log('token_holder_jobs table exists:', (tokenHolderJobsExists as any)[0][0].count > 0);

    // Check if balance_check_logs table exists
    const balanceCheckLogsExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'balance_check_logs'
    `);
    console.log('balance_check_logs table exists:', (balanceCheckLogsExists as any)[0][0].count > 0);
  } catch (error) {
    console.error('Error checking tables:', error);
    process.exit(1);
  }
}

// Run the check function
checkTables()
  .then(() => {
    console.log('Table check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error during table check:', error);
    process.exit(1);
  });
