import { config } from "dotenv";
import { db } from './drizzle';
import { systemSettings } from './schema';
import { sql, eq } from 'drizzle-orm';

config({ path: ".env" });

// Chain mapping based on config/index.tsx networks
const CHAIN_MAPPING = {
  'cronos': '25',
  'mainnet': '1',
  'arbitrum': '42161',
  'sepolia': '11155111',
  'solana': '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp',
  'cronoszkEVM': '388'
};

const CHAIN_NAMES = {
  '25': 'Cronos',
  '1': 'Ethereum',
  '42161': 'Arbitrum',
  '11155111': 'Sepolia',
  'sol1': 'Solana',
  '388': 'Cronos zkEVM'
};

/**
 * Default component configuration that will be applied to all chains
 */
const DEFAULT_COMPONENTS = [
  {
    componentType: 'bannerpfp',
    order: '1',
    hidden: 'N',
    details: {
      backgroundColor: 'transparent',
      fontColor: null,
      profileName: null,
      profileBio: null,
      profileShape: 'circular',
      profileHorizontalPosition: 20,
      profileNameHorizontalPosition: 50,
      profileNameStyle: {
        fontSize: '1.5rem',
        fontWeight: 'bold',
        fontColor: '#ffffff',
        effect: 'typewriter'
      },
      defaultBannerImagePath: 'banner.png',
      defaultProfileImagePath: 'pfp.jpg'
    }
  },
  {
    componentType: 'hero',
    order: '2',
    hidden: 'N',
    details: {
      backgroundColor: 'transparent',
      fontColor: null,
      heroText: 'Welcome to Web3',
      heroSubtext: 'Building the future of social networking',
      heroTextStyle: {
        fontSize: '2rem',
        fontWeight: 'bold',
        fontColor: '#ffffff',
        effect: 'decrypted'
      },
      heroSubtextStyle: {
        fontSize: '1rem',
        fontWeight: 'normal',
        fontColor: '#cccccc',
        effect: 'no effect'
      },
      defaultHeroImagePath: 'hero.jpg'
    }
  },
  {
    componentType: 'socialLinks',
    order: '3',
    hidden: 'N',
    details: {
      backgroundColor: 'transparent',
      fontColor: null,
      socialLinks: {
        twitter: '',
        discord: '',
        telegram: '',
        website: '',
        facebook: '',
        youtube: '',
        email: '',
        linkedin: '',
        cro: ''
      }
    }
  },
  {
    componentType: 'about',
    order: '4',
    hidden: 'N',
    details: {
      backgroundColor: 'transparent',
      fontColor: null,
      aboutText: 'Passionate about Web3 and decentralized technologies. Join me on this journey!'
    }
  }
];

/**
 * Default profile settings that will be applied to all chains
 */
const DEFAULT_PROFILE_SETTINGS = {
  default_role: 'user',
  default_status: 'new',
  default_expiry_days: '30',
  default_profile_name_format: 'Web3 User {address}',
  default_profile_bio: 'Welcome to my Web3 Social profile!'
};

/**
 * Create default component settings for a specific chain
 */
async function createDefaultsForChain(chainId: string, chainName: string): Promise<void> {
  try {
    console.log(`Creating defaults for chain: ${chainName} (${chainId})`);

    // Create component defaults for this chain
    const componentDefaultsId = `component_defaults_${chainId}`;
    await db.insert(systemSettings).values({
      id: componentDefaultsId,
      value: {
        defaults: DEFAULT_COMPONENTS
      }
    }).onDuplicateKeyUpdate({
      set: {
        value: {
          defaults: DEFAULT_COMPONENTS
        },
        updatedAt: new Date()
      }
    });

    // Create profile defaults for this chain
    const profileDefaultsId = `profile_defaults_${chainId}`;
    await db.insert(systemSettings).values({
      id: profileDefaultsId,
      value: DEFAULT_PROFILE_SETTINGS
    }).onDuplicateKeyUpdate({
      set: {
        value: DEFAULT_PROFILE_SETTINGS,
        updatedAt: new Date()
      }
    });

    // Create token requirements for this chain
    const tokenRequirementsId = `token_requirements_${chainId}`;
    await db.insert(systemSettings).values({
      id: tokenRequirementsId,
      value: {
        mainRequirement: {
          chainId,
          chainName,
          tokenAddress: chainId === '25' ? '0xe68892c424E4f0EDE343F6F05c873F7b7a528048' : '',
          tokenName: 'Web3Tools',
          minimumHoldings: ''
        },
        secondaryRequirements: []
      }
    }).onDuplicateKeyUpdate({
      set: {
        value: {
          mainRequirement: {
            chainId,
            chainName,
            tokenAddress: chainId === '25' ? '0xe68892c424E4f0EDE343F6F05c873F7b7a528048' : '',
            tokenName: 'Web3Tools',
            minimumHoldings: ''
          },
          secondaryRequirements: []
        },
        updatedAt: new Date()
      }
    });

    // Create featured profile setting for this chain
    const featuredProfileId = `featured_profile_${chainId}`;
    await db.insert(systemSettings).values({
      id: featuredProfileId,
      value: 'web3tools'
    }).onDuplicateKeyUpdate({
      set: {
        value: 'web3tools',
        updatedAt: new Date()
      }
    });

    console.log(`✅ Successfully created defaults for ${chainName} (${chainId})`);
  } catch (error) {
    console.error(`❌ Error creating defaults for ${chainName} (${chainId}):`, error);
    throw error;
  }
}

/**
 * Main function to create defaults for all chains
 */
async function createDefaultsForAllChains(): Promise<void> {
  try {
    console.log('🚀 Starting to create default components for all chains...');
    console.log('📋 Configured chains:', Object.entries(CHAIN_NAMES));

    let successCount = 0;
    let errorCount = 0;

    // Process each chain
    for (const [chainId, chainName] of Object.entries(CHAIN_NAMES)) {
      try {
        await createDefaultsForChain(chainId, chainName);
        successCount++;
      } catch (error) {
        console.error(`Failed to create defaults for ${chainName} (${chainId}):`, error);
        errorCount++;
      }
    }

    console.log('\n📊 Summary:');
    console.log(`✅ Successfully processed: ${successCount} chains`);
    console.log(`❌ Failed: ${errorCount} chains`);

    if (errorCount === 0) {
      console.log('🎉 All chains processed successfully!');
    } else {
      console.log('⚠️  Some chains failed to process. Check the errors above.');
    }

  } catch (error) {
    console.error('💥 Fatal error creating defaults for all chains:', error);
    throw error;
  }
}

// Export for use in other scripts
export default createDefaultsForAllChains;

// Run directly if this script is executed directly
if (require.main === module) {
  createDefaultsForAllChains()
    .then(() => {
      console.log('✨ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}
