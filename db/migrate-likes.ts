import { config } from "dotenv";
import { db } from './drizzle';
import { sql } from 'drizzle-orm';

config({ path: ".env" });

/**
 * Migration script to add the profileLikes table
 * This will create the profileLikes table if it doesn't exist
 */
async function migrateLikesTable() {
  try {
    console.log('Starting migration to add profileLikes table...');

    // Check if the profileLikes table exists
    try {
      console.log('Checking if profileLikes table exists...');
      await db.execute(`SELECT 1 FROM profileLikes LIMIT 1`);
      console.log('profileLikes table already exists');
    } catch (error) {
      console.log('profileLikes table does not exist, creating it...');
      
      // Create the profileLikes table
      await db.execute(sql`
        CREATE TABLE profileLikes (
          id VARCHAR(36) PRIMARY KEY NOT NULL,
          liker_address VARCHAR(255) NOT NULL,
          liked_address VARCHAR(255) NOT NULL,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (liker_address) REFERENCES web3Profile(address) ON DELETE CASCADE,
          FOREIGN KEY (liked_address) REFERENCES web3Profile(address) ON DELETE CASCADE,
          UNIQUE KEY unique_like_idx (liker_address, liked_address)
        )
      `);
      
      console.log('profileLikes table created successfully');
    }

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
migrateLikesTable();
