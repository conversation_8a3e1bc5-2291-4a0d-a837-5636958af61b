import { config } from "dotenv";
import { db } from './drizzle';
import { sql } from 'drizzle-orm';

config({ path: ".env" });

/**
 * Migration script to add referral fields to web3Profile table and create profileReferrals table
 */
async function migrateReferrals() {
  try {
    console.log('Starting migration to add referral system...');

    // Add referral fields to web3Profile table
    console.log('1. Adding referral fields to web3Profile table...');
    
    try {
      // Check if referralCode column exists
      await db.execute(sql`SELECT referral_code FROM web3Profile LIMIT 1`);
      console.log('referralCode column already exists');
    } catch (error) {
      console.log('Adding referralCode column...');
      await db.execute(sql`
        ALTER TABLE web3Profile 
        ADD COLUMN referral_code VARCHAR(8) NULL
      `);
      
      // Add unique index for referral code
      await db.execute(sql`
        CREATE UNIQUE INDEX referral_code_idx ON web3Profile(referral_code)
      `);
      
      console.log('referralCode column added successfully');
    }

    try {
      // Check if referredBy column exists
      await db.execute(sql`SELECT referred_by FROM web3Profile LIMIT 1`);
      console.log('referredBy column already exists');
    } catch (error) {
      console.log('Adding referredBy column...');
      await db.execute(sql`
        ALTER TABLE web3Profile 
        ADD COLUMN referred_by VARCHAR(8) NULL
      `);
      console.log('referredBy column added successfully');
    }

    // Create profileReferrals table
    console.log('2. Creating profileReferrals table...');
    
    try {
      await db.execute(sql`SELECT 1 FROM profileReferrals LIMIT 1`);
      console.log('profileReferrals table already exists');
    } catch (error) {
      console.log('Creating profileReferrals table...');
      
      await db.execute(sql`
        CREATE TABLE profileReferrals (
          id VARCHAR(36) PRIMARY KEY NOT NULL,
          referrer_address VARCHAR(255) NOT NULL,
          referred_address VARCHAR(255) NOT NULL,
          referral_code VARCHAR(8) NOT NULL,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (referrer_address) REFERENCES web3Profile(address) ON DELETE CASCADE,
          FOREIGN KEY (referred_address) REFERENCES web3Profile(address) ON DELETE CASCADE,
          UNIQUE KEY unique_referral_idx (referred_address)
        )
      `);
      
      console.log('profileReferrals table created successfully');
    }

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
migrateReferrals();
