-- Migration to add token holder tracking tables
-- Run this SQL in your MySQL database

-- Token holder snapshots table - stores metadata about each token holder fetch
CREATE TABLE IF NOT EXISTS `token_holder_snapshots` (
  `id` int NOT NULL AUTO_INCREMENT,
  `contract_address` varchar(50) NOT NULL,
  `chain_id` varchar(40) NOT NULL,
  `token_name` varchar(100),
  `snapshot_date` date NOT NULL,
  `total_holders` int NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `contract_date_idx` (`contract_address`, `snapshot_date`),
  KEY `chain_contract_idx` (`chain_id`, `contract_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Individual token holders table - stores the actual holder data
CREATE TABLE IF NOT EXISTS `token_holders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `snapshot_id` int NOT NULL,
  `holder_address` varchar(50) NOT NULL,
  `balance` varchar(50) NOT NULL,
  `balance_numeric` decimal(36,18),
  `pending_balance_update` varchar(10) DEFAULT 'No',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `snapshot_holder_idx` (`snapshot_id`, `holder_address`),
  KEY `holder_address_idx` (`holder_address`),
  KEY `balance_idx` (`balance_numeric`),
  CONSTRAINT `token_holders_snapshot_fk` FOREIGN KEY (`snapshot_id`) REFERENCES `token_holder_snapshots` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Token holder jobs tracking table - tracks the status of fetch operations
CREATE TABLE IF NOT EXISTS `token_holder_jobs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `contract_address` varchar(50) NOT NULL,
  `chain_id` varchar(40) NOT NULL,
  `token_name` varchar(100),
  `status` varchar(20) DEFAULT 'pending',
  `started_at` timestamp NULL,
  `completed_at` timestamp NULL,
  `error_message` text,
  `total_holders` int,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
