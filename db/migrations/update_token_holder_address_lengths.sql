-- Migration to update token holder tables for multichain support
-- This increases the length of address and chain ID fields to support Solana addresses
-- Run this SQL in your MySQL database

-- Update token_holder_snapshots table
ALTER TABLE `token_holder_snapshots`
MODIFY COLUMN `contract_address` varchar(70) NOT NULL,
MODIFY COLUMN `chain_id` varchar(40) NOT NULL;

-- Update token_holders table
ALTER TABLE `token_holders`
MODIFY COLUMN `holder_address` varchar(70) NOT NULL;

-- Update token_holder_jobs table
ALTER TABLE `token_holder_jobs`
MODIFY COLUMN `contract_address` varchar(70) NOT NULL,
MODIFY COLUMN `chain_id` varchar(40) NOT NULL;
