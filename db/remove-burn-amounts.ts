import { db } from './drizzle';
import { systemSettings } from './schema';
import { like, eq } from 'drizzle-orm';

/**
 * <PERSON><PERSON><PERSON> to remove burnAmount fields from token requirements in the database
 */
async function removeBurnAmounts() {
  try {
    console.log('🔍 Finding token requirements with burn amounts...');
    
    // Get all token requirements settings
    const tokenRequirements = await db
      .select()
      .from(systemSettings)
      .where(like(systemSettings.id, 'token_requirements_%'));

    console.log(`Found ${tokenRequirements.length} token requirements records`);

    let updatedCount = 0;

    for (const setting of tokenRequirements) {
      try {
        const value = setting.value as any;
        
        if (value && typeof value === 'object') {
          let needsUpdate = false;
          
          // Check if mainRequirement has burnAmount
          if (value.mainRequirement && value.mainRequirement.burnAmount !== undefined) {
            console.log(`🔧 Removing burnAmount from ${setting.id} mainRequirement`);
            delete value.mainRequirement.burnAmount;
            needsUpdate = true;
          }
          
          // Check if secondaryRequirements have burnAmount
          if (value.secondaryRequirements && Array.isArray(value.secondaryRequirements)) {
            for (const secReq of value.secondaryRequirements) {
              if (secReq.burnAmount !== undefined) {
                console.log(`🔧 Removing burnAmount from ${setting.id} secondaryRequirement`);
                delete secReq.burnAmount;
                needsUpdate = true;
              }
            }
          }
          
          // Check for legacy burnAmounts field
          if (value.burnAmounts !== undefined) {
            console.log(`🔧 Removing legacy burnAmounts field from ${setting.id}`);
            delete value.burnAmounts;
            needsUpdate = true;
          }
          
          if (needsUpdate) {
            // Update the database record
            await db.update(systemSettings)
              .set({
                value: value,
                updatedAt: new Date()
              })
              .where(eq(systemSettings.id, setting.id));
            
            updatedCount++;
            console.log(`✅ Updated ${setting.id}`);
          } else {
            console.log(`✓ ${setting.id} already clean`);
          }
        }
      } catch (error) {
        console.error(`❌ Error processing ${setting.id}:`, error);
      }
    }

    console.log(`\n🎉 Cleanup complete! Updated ${updatedCount} records.`);
    
    // Verify the cleanup
    console.log('\n🔍 Verifying cleanup...');
    const verifyRequirements = await db
      .select()
      .from(systemSettings)
      .where(like(systemSettings.id, 'token_requirements_%'));

    for (const setting of verifyRequirements) {
      const value = setting.value as any;
      if (value && typeof value === 'object') {
        const hasBurnAmount = 
          (value.mainRequirement && value.mainRequirement.burnAmount !== undefined) ||
          (value.secondaryRequirements && value.secondaryRequirements.some((req: any) => req.burnAmount !== undefined)) ||
          (value.burnAmounts !== undefined);
        
        if (hasBurnAmount) {
          console.log(`⚠️  ${setting.id} still has burn amounts!`);
        } else {
          console.log(`✅ ${setting.id} is clean`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  }
}

// Run the cleanup if this script is executed directly
if (require.main === module) {
  removeBurnAmounts()
    .then(() => {
      console.log('✅ Burn amount cleanup completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Burn amount cleanup failed:', error);
      process.exit(1);
    });
}

export { removeBurnAmounts };
