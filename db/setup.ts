import { config } from "dotenv";
import { db } from './drizzle';
import { sql } from 'drizzle-orm';

config({ path: ".env" });

/**
 * Setup script for initializing the database on a new environment
 * This will create all necessary tables and indexes
 */
async function setupDatabase() {
  try {
    console.log('Setting up database...');

    // Create web3Profile table
    console.log('1. Creating web3Profile table...');
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS web3Profile (
        address VARCHAR(255) PRIMARY KEY,
        \`chain\` VARCHAR(255) NOT NULL,
        \`name\` VARCHAR(255) NOT NULL UNIQUE,
        bio TEXT NOT NULL,
        social_links JSON,
        component_positions JSON,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create componentPositions table with image data fields
    console.log('2. Creating componentPositions table with image data fields...');
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS componentPositions (
        address VARCHAR(255) NOT NULL,
        \`chain\`  VARCHAR(255) NOT NULL,
        component_type VARCHAR(50) NOT NULL,
        \`order\` VARCHAR(10) NOT NULL,
        hidden VARCHAR(1) NOT NULL DEFAULT 'N',
        image_data LONGTEXT,
        scale DECIMAL(10,2) DEFAULT 1,
        position_x INT DEFAULT 0,
        position_y INT DEFAULT 0,
        natural_width INT,
        natural_height INT,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (address, component_type),
        FOREIGN KEY (address) REFERENCES web3Profile(address) ON DELETE CASCADE
      )
    `);

    // Create waitingList table
    console.log('3. Creating waitingList table...');
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS waitingList (
        address VARCHAR(255) PRIMARY KEY,
        \`chain\` VARCHAR(255) NOT NULL,
        xHandle TEXT NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Note: profileImages and bannerImages tables have been removed
    // Their functionality has been moved to the componentPositions table

    // Create indexes
    console.log('4. Creating indexes...');
    try {
      await db.execute(sql`
        CREATE INDEX IF NOT EXISTS componentPositions_address_idx ON componentPositions (address)
      `);
    } catch (error) {
      console.log('Index componentPositions_address_idx might already exist, continuing...');
    }

    // Note: Indexes for profileImages and bannerImages have been removed

    try {
      await db.execute(sql`
        CREATE INDEX IF NOT EXISTS waitingList_address_idx ON waitingList (address)
      `);
    } catch (error) {
      console.log('Index waitingList_address_idx might already exist, continuing...');
    }

    console.log('Database setup completed successfully!');
  } catch (error) {
    console.error('Database setup failed:', error);
  } finally {
    process.exit(0);
  }
}

setupDatabase();
