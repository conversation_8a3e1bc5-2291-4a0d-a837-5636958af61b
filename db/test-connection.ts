import { config } from "dotenv";
import { db } from './drizzle';
import { sql } from 'drizzle-orm';
import { web3Profile } from './schema';
import { eq } from 'drizzle-orm';

config({ path: ".env" });

/**
 * Test script to verify database connection and check admin user
 */
async function testConnection() {
  try {
    console.log('Testing database connection...');
    console.log('Connection string:', process.env.DATABASE_URL?.replace(/(mysql:\/\/[^:]+:)[^@]+(@.+)/, '$1*****$2')); // Hide password

    // Try a simple query
    const result = await db.execute(sql`SELECT 1 as test`);
    console.log('Database connection successful!');
    console.log('Result:', result);

    // Check for the specific admin address
    const adminAddress = '0xfD0f2192C6666389a09E5325772f7f5a8eF6655F';
    console.log(`\nChecking for admin address: ${adminAddress}`);

    const adminUser = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, adminAddress));

    if (adminUser.length > 0) {
      console.log('Admin user found:', adminUser[0]);
    } else {
      console.log('Admin user NOT found in database');
    }

    // List all users and their roles
    console.log('\nAll users in database:');
    const allUsers = await db.select().from(web3Profile);
    console.log(`Total users: ${allUsers.length}`);
    allUsers.forEach(user => {
      console.log(`- ${user.address}: role=${user.role}, status=${user.status}`);
    });

    // List all tables
    console.log('\nChecking database tables...');
    const tables = await db.execute(sql`SHOW TABLES`);
    console.log('Tables result:', tables);

    console.log('Connection test completed successfully!');
  } catch (error) {
    console.error('Database connection failed:', error);
  } finally {
    process.exit(0);
  }
}

testConnection();
