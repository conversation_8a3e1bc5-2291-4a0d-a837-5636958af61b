// Test script for sniper API endpoints
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testSniperAPIs() {
  console.log('🧪 Testing Sniper API Endpoints...\n');

  try {
    // Test 1: Get creators (should return empty array initially)
    console.log('1. Testing GET /api/sniper/creators');
    const creatorsResponse = await fetch(`${BASE_URL}/api/sniper/creators?chainId=25`);
    const creatorsData = await creatorsResponse.json();
    console.log('✅ Creators response:', creatorsData);
    console.log('');

    // Test 2: Add a creator
    console.log('2. Testing POST /api/sniper/creators');
    const addCreatorResponse = await fetch(`${BASE_URL}/api/sniper/creators`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        address: '******************************************',
        name: 'Test Creator',
        chainId: '25'
      })
    });
    const addCreatorData = await addCreatorResponse.json();
    console.log('✅ Add creator response:', addCreatorData);
    console.log('');

    // Test 3: Get creators again (should now have one creator)
    console.log('3. Testing GET /api/sniper/creators (after adding)');
    const creatorsResponse2 = await fetch(`${BASE_URL}/api/sniper/creators?chainId=25`);
    const creatorsData2 = await creatorsResponse2.json();
    console.log('✅ Creators response (after adding):', creatorsData2);
    console.log('');

    // Test 4: Get watched addresses
    console.log('4. Testing GET /api/sniper/watched-addresses');
    const watchedResponse = await fetch(`${BASE_URL}/api/sniper/watched-addresses?chainId=25`);
    const watchedData = await watchedResponse.json();
    console.log('✅ Watched addresses response:', watchedData);
    console.log('');

    // Test 5: Add a watched address
    console.log('5. Testing POST /api/sniper/watched-addresses');
    const addWatchedResponse = await fetch(`${BASE_URL}/api/sniper/watched-addresses`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        address: '******************************************',
        name: 'Test Watched Address',
        chainId: '25'
      })
    });
    const addWatchedData = await addWatchedResponse.json();
    console.log('✅ Add watched address response:', addWatchedData);
    console.log('');

    // Test 6: Test config endpoint
    console.log('6. Testing GET /api/sniper/config');
    const configResponse = await fetch(`${BASE_URL}/api/sniper/config?userAddress=******************************************&chainId=25`);
    const configData = await configResponse.json();
    console.log('✅ Config response:', configData);
    console.log('');

    console.log('🎉 All API tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the tests
testSniperAPIs();
